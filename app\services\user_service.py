from typing import Optional
from sqlalchemy.orm import Session
from app.models.user import User, VerificationLevel
from app.models.order import Order, OrderStatus
from app.core.security import create_access_token
from app.utils.wechat import wechat_api
from app.utils.sms import sms_service
from datetime import timedelta


class UserService:
    """用户相关业务逻辑"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def login_with_wechat(
        self,
        code: str,
        nickname: str,
        avatar_url: Optional[str] = None
    ) -> tuple[User, str]:
        """
        微信登录
        返回 (用户对象, 访问令牌)
        """
        # 通过code获取openid
        wechat_data = await wechat_api.code_to_session(code)
        if not wechat_data or not wechat_data.get("openid"):
            raise ValueError("微信授权失败")
        
        openid = wechat_data["openid"]
        
        # 查找或创建用户
        user = self.db.query(User).filter(User.openid == openid).first()
        if not user:
            user = User(
                openid=openid,
                nickname=nickname,
                avatar_url=avatar_url,
                verification_level=VerificationLevel.NONE
            )
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
        else:
            # 更新用户信息
            user.nickname = nickname
            if avatar_url:
                user.avatar_url = avatar_url
            self.db.commit()
        
        # 生成访问令牌
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=timedelta(days=7)
        )
        
        return user, access_token
    
    def update_user_location(
        self,
        user: User,
        latitude: float,
        longitude: float,
        address: Optional[str] = None
    ) -> User:
        """更新用户位置信息"""
        user.latitude = latitude
        user.longitude = longitude
        if address:
            user.address = address
        
        self.db.commit()
        self.db.refresh(user)
        return user
    
    async def verify_phone(self, user: User, phone: str, code: str) -> bool:
        """验证手机号"""
        # 验证验证码
        if not sms_service.verify_code(phone, code):
            return False
        
        # 检查手机号是否已被其他用户使用
        existing_user = self.db.query(User).filter(
            User.phone == phone,
            User.id != user.id
        ).first()
        
        if existing_user:
            return False
        
        # 更新用户手机号和认证等级
        user.phone = phone
        if user.verification_level == VerificationLevel.NONE:
            user.verification_level = VerificationLevel.BASIC
            user.is_verified = True
        
        self.db.commit()
        return True
    
    def update_real_name_info(
        self,
        user: User,
        real_name: str,
        id_card: str
    ) -> User:
        """更新实名信息"""
        user.real_name = real_name
        user.id_card = id_card
        
        # 如果是基础认证，升级到高级认证
        if user.verification_level == VerificationLevel.BASIC:
            user.verification_level = VerificationLevel.ADVANCED
        
        self.db.commit()
        self.db.refresh(user)
        return user
    
    def calculate_user_credit_score(self, user: User) -> float:
        """
        计算用户信用评分
        基于完成订单数量、评价等
        """
        # 获取用户作为零工的订单
        worker_orders = self.db.query(Order).filter(
            Order.worker_id == user.id,
            Order.status == OrderStatus.COMPLETED
        ).all()
        
        # 获取用户作为商户的订单
        merchant_orders = self.db.query(Order).filter(
            Order.merchant_id == user.id,
            Order.status == OrderStatus.COMPLETED
        ).all()
        
        total_orders = len(worker_orders) + len(merchant_orders)
        if total_orders == 0:
            return 100.0  # 默认分数
        
        # 计算平均评分
        total_rating = 0
        rating_count = 0
        
        for order in worker_orders:
            if order.worker_rating:
                total_rating += order.worker_rating
                rating_count += 1
        
        for order in merchant_orders:
            if order.merchant_rating:
                total_rating += order.merchant_rating
                rating_count += 1
        
        if rating_count == 0:
            average_rating = 5.0  # 默认满分
        else:
            average_rating = total_rating / rating_count
        
        # 基础分数 + 订单数量加分 + 评分加分
        base_score = 60.0
        order_bonus = min(total_orders * 2, 30)  # 最多30分
        rating_bonus = (average_rating - 3) * 5  # 3分以上每分加5分
        
        credit_score = base_score + order_bonus + rating_bonus
        credit_score = max(0, min(100, credit_score))  # 限制在0-100之间
        
        return round(credit_score, 1)
    
    def update_user_credit_score(self, user: User) -> User:
        """更新用户信用评分"""
        new_score = self.calculate_user_credit_score(user)
        user.credit_score = new_score
        self.db.commit()
        self.db.refresh(user)
        return user
    
    def get_user_statistics(self, user: User) -> dict:
        """获取用户统计信息"""
        # 作为零工的统计
        worker_completed = self.db.query(Order).filter(
            Order.worker_id == user.id,
            Order.status == OrderStatus.COMPLETED
        ).count()
        
        worker_total = self.db.query(Order).filter(
            Order.worker_id == user.id
        ).count()
        
        # 作为商户的统计
        merchant_completed = self.db.query(Order).filter(
            Order.merchant_id == user.id,
            Order.status == OrderStatus.COMPLETED
        ).count()
        
        merchant_total = self.db.query(Order).filter(
            Order.merchant_id == user.id
        ).count()
        
        return {
            "verification_level": user.verification_level.value,
            "credit_score": user.credit_score,
            "as_worker": {
                "completed_orders": worker_completed,
                "total_orders": worker_total,
                "completion_rate": worker_completed / worker_total if worker_total > 0 else 0
            },
            "as_merchant": {
                "completed_orders": merchant_completed,
                "total_orders": merchant_total,
                "completion_rate": merchant_completed / merchant_total if merchant_total > 0 else 0
            }
        }
