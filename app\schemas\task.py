from pydantic import BaseModel, Field, validator
from typing import Optional, List, Union
from datetime import datetime, date
from app.models.task import TaskCategory, TaskStatus


class TaskBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1)
    category: TaskCategory
    reward: float = Field(..., gt=0, description="报酬金额，必须大于0")
    estimated_hours: Optional[float] = Field(None, gt=0, description="预估工时")
    deadline: Optional[Union[datetime, date, str]] = None
    address: str = Field(..., min_length=1, max_length=200)
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    auto_accept: bool = Field(True, description="是否自动接单")

    @validator('deadline', pre=True)
    def parse_deadline(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            try:
                # 尝试解析日期字符串
                if len(v) == 10:  # YYYY-MM-DD格式
                    return datetime.strptime(v, '%Y-%m-%d')
                else:  # 包含时间的格式
                    return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError(f"无效的日期格式: {v}")
        elif isinstance(v, date) and not isinstance(v, datetime):
            # 将date转换为datetime
            return datetime.combine(v, datetime.min.time())
        return v


class TaskCreate(TaskBase):
    pass


class TaskUpdate(BaseModel):
    task_id: int = Field(..., description="任务ID")
    title: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1)
    category: Optional[TaskCategory] = None
    reward: Optional[float] = Field(None, gt=0)
    estimated_hours: Optional[float] = Field(None, gt=0)
    deadline: Optional[Union[datetime, date, str]] = None
    address: Optional[str] = Field(None, min_length=1, max_length=200)
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)
    auto_accept: Optional[bool] = None

    @validator('deadline', pre=True)
    def parse_deadline(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            try:
                # 尝试解析日期字符串
                if len(v) == 10:  # YYYY-MM-DD格式
                    return datetime.strptime(v, '%Y-%m-%d')
                else:  # 包含时间的格式
                    return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError(f"无效的日期格式: {v}")
        elif isinstance(v, date) and not isinstance(v, datetime):
            # 将date转换为datetime
            return datetime.combine(v, datetime.min.time())
        return v


class TaskDelete(BaseModel):
    task_id: int = Field(..., description="要删除的任务ID")


class TaskResponse(TaskBase):
    id: int
    publisher_id: int
    status: TaskStatus
    created_at: datetime
    updated_at: Optional[datetime]
    
    # 发布者信息
    publisher_nickname: Optional[str] = None
    publisher_avatar: Optional[str] = None
    publisher_credit_score: Optional[float] = None
    
    # 距离信息（查询时计算）
    distance: Optional[float] = Field(None, description="距离（公里）")

    class Config:
        from_attributes = True


class TaskListResponse(BaseModel):
    tasks: List[TaskResponse]
    total: int
    page: int
    size: int


# 任务查询参数
class TaskQueryParams(BaseModel):
    page: int = Field(1, ge=1)
    size: int = Field(20, ge=1, le=100)
    category: Optional[TaskCategory] = None
    min_reward: Optional[float] = Field(None, ge=0)
    max_reward: Optional[float] = Field(None, ge=0)
    max_distance: Optional[float] = Field(None, ge=0, description="最大距离（公里）")
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)
    status: Optional[TaskStatus] = None
    publisher_id: Optional[int] = None


# 附近任务查询（零工端）
class NearbyTasksRequest(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    max_distance: float = Field(10.0, ge=0.1, le=100, description="最大距离（公里）")
    category: Optional[TaskCategory] = None
    min_reward: Optional[float] = Field(None, ge=0)
    max_reward: Optional[float] = Field(None, ge=0)
    page: int = Field(1, ge=1)
    size: int = Field(20, ge=1, le=50)
