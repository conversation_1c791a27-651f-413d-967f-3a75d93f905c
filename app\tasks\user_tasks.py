from app.core.celery_app import celery_app
from app.core.database import <PERSON><PERSON>ocal
from app.models.user import User
from app.services.user_service import UserService


@celery_app.task
def update_user_credit_score(user_id: int):
    """更新用户信用评分"""
    try:
        db = SessionLocal()
        user_service = UserService(db)
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return {"status": "failed", "error": "用户不存在"}
        
        # 更新信用评分
        updated_user = user_service.update_user_credit_score(user)
        
        db.close()
        return {
            "status": "success", 
            "user_id": user_id,
            "new_credit_score": updated_user.credit_score
        }
        
    except Exception as e:
        db.close()
        return {"status": "failed", "error": str(e)}


@celery_app.task
def batch_update_credit_scores():
    """批量更新所有用户信用评分"""
    try:
        db = SessionLocal()
        user_service = UserService(db)
        
        # 获取所有活跃用户
        users = db.query(User).filter(User.is_active == True).all()
        
        updated_count = 0
        for user in users:
            try:
                user_service.update_user_credit_score(user)
                updated_count += 1
            except Exception as e:
                print(f"更新用户 {user.id} 信用评分失败: {e}")
                continue
        
        db.close()
        return {
            "status": "success",
            "total_users": len(users),
            "updated_count": updated_count
        }
        
    except Exception as e:
        db.close()
        return {"status": "failed", "error": str(e)}


@celery_app.task
def cleanup_inactive_users():
    """清理不活跃用户数据"""
    try:
        db = SessionLocal()
        
        # 这里可以添加清理逻辑，比如：
        # 1. 删除长期未登录的用户
        # 2. 清理过期的验证码
        # 3. 清理临时数据等
        
        # 示例：清理30天未登录的未认证用户
        from datetime import datetime, timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        inactive_users = db.query(User).filter(
            User.is_verified == False,
            User.updated_at < cutoff_date
        ).all()
        
        deleted_count = 0
        for user in inactive_users:
            # 检查用户是否有相关订单，如果有则不删除
            from app.models.order import Order
            has_orders = db.query(Order).filter(
                (Order.worker_id == user.id) | (Order.merchant_id == user.id)
            ).first()
            
            if not has_orders:
                db.delete(user)
                deleted_count += 1
        
        db.commit()
        db.close()
        
        return {
            "status": "success",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        db.close()
        return {"status": "failed", "error": str(e)}
