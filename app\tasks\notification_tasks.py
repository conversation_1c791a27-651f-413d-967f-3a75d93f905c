from celery import current_task
from app.core.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.user import User
from app.models.task import Task
from app.models.order import Order
from app.utils.wechat import wechat_api
import httpx
from typing import Dict, Any


@celery_app.task(bind=True, max_retries=3)
def send_wechat_template_message(
    self,
    user_id: int,
    template_id: str,
    data: Dict[str, Any],
    page: str = ""
):
    """
    发送微信小程序模板消息
    """
    import asyncio

    async def _send_message():
        try:
            db = SessionLocal()
            user = db.query(User).filter(User.id == user_id).first()

            if not user or not user.openid:
                raise ValueError(f"用户不存在或openid为空: {user_id}")

            # 获取access_token
            access_token = await wechat_api.get_access_token()
            if not access_token:
                raise ValueError("获取access_token失败")

            # 发送模板消息
            url = f"https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={access_token}"

            payload = {
                "touser": user.openid,
                "template_id": template_id,
                "page": page,
                "data": data
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload)
                response.raise_for_status()
                result = response.json()

                if result.get("errcode", 0) != 0:
                    raise ValueError(f"微信API错误: {result}")

            db.close()
            return {"status": "success", "message": "模板消息发送成功"}

        except Exception as exc:
            db.close()
            raise exc

    try:
        # 在Celery任务中运行异步函数
        result = asyncio.run(_send_message())
        return result

    except Exception as exc:
        print(f"发送模板消息失败: {exc}")

        # 重试机制
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {"status": "failed", "error": str(exc)}


@celery_app.task
def send_task_accepted_notification(task_id: int, worker_id: int):
    """任务被接单通知"""
    try:
        db = SessionLocal()
        task = db.query(Task).filter(Task.id == task_id).first()
        worker = db.query(User).filter(User.id == worker_id).first()
        
        if not task or not worker:
            return {"status": "failed", "error": "任务或用户不存在"}
        
        # 构造模板消息数据
        template_data = {
            "thing1": {"value": task.title[:20]},  # 任务标题
            "name2": {"value": worker.nickname[:20]},  # 接单人
            "time3": {"value": task.created_at.strftime("%Y-%m-%d %H:%M")},  # 时间
            "amount4": {"value": f"¥{task.reward}"},  # 报酬
        }
        
        # 发送给任务发布者
        send_wechat_template_message.delay(
            user_id=task.publisher_id,
            template_id="TASK_ACCEPTED_TEMPLATE_ID",  # 需要在微信后台配置
            data=template_data,
            page=f"pages/task/detail?id={task_id}"
        )
        
        db.close()
        return {"status": "success"}
        
    except Exception as e:
        db.close()
        return {"status": "failed", "error": str(e)}


@celery_app.task
def send_task_completed_notification(order_id: int):
    """任务完成通知"""
    try:
        db = SessionLocal()
        order = db.query(Order).join(Task).filter(Order.id == order_id).first()
        
        if not order:
            return {"status": "failed", "error": "订单不存在"}
        
        # 构造模板消息数据
        template_data = {
            "thing1": {"value": order.task.title[:20]},  # 任务标题
            "time2": {"value": order.completed_at.strftime("%Y-%m-%d %H:%M")},  # 完成时间
            "amount3": {"value": f"¥{order.task.reward}"},  # 报酬
        }
        
        # 发送给双方
        send_wechat_template_message.delay(
            user_id=order.worker_id,
            template_id="TASK_COMPLETED_TEMPLATE_ID",
            data=template_data,
            page=f"pages/order/detail?id={order_id}"
        )
        
        send_wechat_template_message.delay(
            user_id=order.merchant_id,
            template_id="TASK_COMPLETED_TEMPLATE_ID",
            data=template_data,
            page=f"pages/order/detail?id={order_id}"
        )
        
        db.close()
        return {"status": "success"}
        
    except Exception as e:
        db.close()
        return {"status": "failed", "error": str(e)}


@celery_app.task
def send_order_status_notification(order_id: int, status: str):
    """订单状态变更通知"""
    try:
        db = SessionLocal()
        order = db.query(Order).join(Task).filter(Order.id == order_id).first()
        
        if not order:
            return {"status": "failed", "error": "订单不存在"}
        
        status_messages = {
            "accepted": "任务已被接单",
            "in_progress": "任务已开始",
            "completed": "任务已完成",
            "cancelled": "任务已取消"
        }
        
        message = status_messages.get(status, "订单状态已更新")
        
        # 构造模板消息数据
        template_data = {
            "thing1": {"value": order.task.title[:20]},  # 任务标题
            "phrase2": {"value": message},  # 状态信息
            "time3": {"value": order.updated_at.strftime("%Y-%m-%d %H:%M")},  # 更新时间
        }
        
        # 发送给相关用户
        if status in ["accepted", "in_progress"]:
            # 发送给任务发布者
            send_wechat_template_message.delay(
                user_id=order.merchant_id,
                template_id="ORDER_STATUS_TEMPLATE_ID",
                data=template_data,
                page=f"pages/order/detail?id={order_id}"
            )
        elif status == "completed":
            # 发送给双方
            send_wechat_template_message.delay(
                user_id=order.worker_id,
                template_id="ORDER_STATUS_TEMPLATE_ID",
                data=template_data,
                page=f"pages/order/detail?id={order_id}"
            )
            send_wechat_template_message.delay(
                user_id=order.merchant_id,
                template_id="ORDER_STATUS_TEMPLATE_ID",
                data=template_data,
                page=f"pages/order/detail?id={order_id}"
            )
        
        db.close()
        return {"status": "success"}
        
    except Exception as e:
        db.close()
        return {"status": "failed", "error": str(e)}
