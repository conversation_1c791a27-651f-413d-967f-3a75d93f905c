# 包工头零工平台 - 项目需求文档

## 项目概述
一个基于微信小程序的零工平台，连接商户和零工，支持任务发布、接单、管理等功能。

## 技术栈
- **前端**: 微信小程序（原生开发）
- **后端**: FastAPI + Python 3.10+
- **数据库**: PostgreSQL + Redis
- **部署**: Docker + Nginx

## 核心功能模块

### 1. 用户身份系统
- 用户可以同时具备商户和零工身份
- 支持身份切换（商户模式/零工模式）
- 微信小程序授权登录
- 基础用户信息管理

### 2. 商户端功能
#### 2.1 任务发布
- 基础任务发布表单
  - 任务标题
  - 任务描述
  - 任务时间（开始时间、截止时间）
  - 任务地点（地址 + 坐标）
  - 报酬金额
  - 任务分类选择
  - 接单模式选择（自动接单/需要确认）

#### 2.2 任务管理
- 我发布的任务列表
- 任务状态查看
- 接单申请处理（需要确认模式）
- 任务完成确认

### 3. 零工端功能
#### 3.1 任务浏览
- 附近任务列表（按距离排序）
- 基础筛选功能
  - 任务类型筛选
  - 报酬范围筛选
  - 距离范围筛选

#### 3.2 接单管理
- 任务详情查看
- 一键接单功能
- 我的接单列表
- 任务完成提交

### 4. 任务分类
- 清洁服务（家庭清洁、办公室清洁、深度清洁）
- 配送服务（外卖配送、快递代取、物品配送）
- 搬运服务（搬家、货物搬运、家具搬运）
- 维修服务（家电维修、水电维修、小型维修）
- 安装服务（家具安装、电器安装、设备安装）
- 其他服务（临时工、活动帮手、其他杂务）

### 5. 订单状态流转
#### 5.1 自动接单模式
```
待接单 → 进行中 → 已完成
```

#### 5.2 需要确认模式
```
待接单 → 已接单 → 进行中 → 已完成
```

#### 5.3 状态说明
- **待接单**: 任务发布后的初始状态
- **已接单**: 零工接单，等待商户确认（仅确认模式）
- **进行中**: 任务正在执行
- **已完成**: 双方确认任务完成
- **已取消**: 任务被取消

### 6. 通知系统
- 微信小程序模板消息推送
- 关键节点通知
  - 任务被接单
  - 任务状态变更
  - 任务完成确认

## 数据库设计

### 用户表 (users)
- id, openid, nickname, avatar_url
- phone, user_type, real_name, id_card
- is_verified, credit_score
- latitude, longitude, address
- is_active, created_at, updated_at

### 任务表 (tasks)
- id, publisher_id, title, description
- category, reward, estimated_hours, deadline
- latitude, longitude, address
- auto_accept, status
- created_at, updated_at

### 订单表 (orders)
- id, task_id, worker_id, merchant_id
- status, accepted_at, started_at, completed_at
- worker_rating, merchant_rating
- worker_comment, merchant_comment
- created_at, updated_at

## 小程序页面结构

### 通用页面
- 登录页面
- 身份选择/切换页面
- 个人中心
- 设置页面

### 商户模式页面
- 任务发布页面
- 我的任务列表
- 任务详情页面
- 接单申请处理页面

### 零工模式页面
- 附近任务列表
- 任务筛选页面
- 任务详情页面
- 我的接单列表

## API接口规划

### 认证相关
- POST /api/v1/auth/login - 微信登录
- GET /api/v1/auth/profile - 获取用户信息
- PUT /api/v1/auth/profile - 更新用户信息

### 任务相关
- POST /api/v1/tasks - 发布任务
- GET /api/v1/tasks - 获取任务列表
- GET /api/v1/tasks/{id} - 获取任务详情
- PUT /api/v1/tasks/{id} - 更新任务
- DELETE /api/v1/tasks/{id} - 删除任务

### 订单相关
- POST /api/v1/orders - 接单
- GET /api/v1/orders - 获取订单列表
- PUT /api/v1/orders/{id} - 更新订单状态
- POST /api/v1/orders/{id}/complete - 完成任务

## 已完成功能
- ✅ 数据库模型设计（用户、任务、订单）
- ✅ Pydantic数据模型定义
- ✅ 用户认证API（微信登录、手机验证）
- ✅ 任务管理API（发布、查询、更新、删除）
- ✅ 订单管理API（接单、状态流转、完成确认）
- ✅ 业务逻辑层（距离计算、状态流转等）
- ✅ Redis缓存集成（任务列表、用户信息缓存）
- ✅ 异步任务处理（Celery + 通知推送）

## 待讨论的功能
1. 支付功能设计
2. 评价系统设计
3. 地理位置精确度要求
4. 实时通知机制
5. 用户认证和实名制
6. 数据缓存策略
7. 并发处理方案
