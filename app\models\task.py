from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, Enum, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class TaskCategory(enum.Enum):
    CLEANING = "cleaning"        # 清洁
    DELIVERY = "delivery"        # 配送
    MOVING = "moving"           # 搬运
    REPAIR = "repair"           # 维修
    ASSEMBLY = "assembly"       # 安装
    OTHER = "other"             # 其他


class TaskStatus(enum.Enum):
    PENDING = "pending"         # 待接单
    ACCEPTED = "accepted"       # 已接单
    IN_PROGRESS = "in_progress" # 进行中
    COMPLETED = "completed"     # 已完成
    CANCELLED = "cancelled"     # 已取消


class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    publisher_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # 任务基本信息
    title = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(Enum(TaskCategory), nullable=False)

    # 报酬和时间
    reward = Column(Float, nullable=False)  # 报酬金额
    estimated_hours = Column(Float)         # 预估工时
    deadline = Column(DateTime(timezone=True))  # 截止时间

    # 地理位置
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    address = Column(String(200), nullable=False)

    # 接单模式
    auto_accept = Column(Boolean, default=True)  # 是否自动接单

    # 状态
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    publisher = relationship("User", foreign_keys=[publisher_id])
    orders = relationship("Order", back_populates="task")
