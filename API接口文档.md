# 包工头零工平台 API 接口文档

## 基础信息

- **Base URL**: `http://127.0.0.1:8080/api/v1`
- **认证方式**: Bear<PERSON>ken (JWT)
- **Content-Type**: `application/json`

## 认证说明

所有接口都需要在请求头中携带认证token：

```
Authorization: Bearer <your_jwt_token>
```

## 任务相关接口

### 1. 接单接口

**接口地址**: `POST /tasks/{task_id}/accept`

**功能描述**: 零工接单

**请求参数**:
- **路径参数**:
  - `task_id` (int): 任务ID

**请求示例**:
```http
POST /api/v1/tasks/3/accept
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**响应格式**:
```json
{
  "id": 1,
  "task_id": 3,
  "worker_id": 2,
  "merchant_id": 1,
  "status": "in_progress",
  "accepted_at": "2024-01-15T10:30:00Z",
  "confirmed_at": null,
  "started_at": "2024-01-15T10:30:00Z",
  "worker_completed_at": null,
  "merchant_completed_at": null,
  "completed_at": null,
  "worker_rating": null,
  "merchant_rating": null,
  "worker_comment": null,
  "merchant_comment": null,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": null,
  "task_title": "清洁办公室",
  "task_description": "需要清洁办公室，包括地面、桌面等",
  "task_category": "cleaning",
  "task_reward": 100.0,
  "task_address": "北京市朝阳区xxx",
  "task_latitude": 39.9042,
  "task_longitude": 116.4074,
  "worker_nickname": "张三",
  "worker_avatar": "https://example.com/avatar1.jpg",
  "worker_credit_score": 4.5,
  "merchant_nickname": "李四",
  "merchant_avatar": "https://example.com/avatar2.jpg",
  "merchant_credit_score": 4.8
}
```

**错误响应**:
- `404 Not Found`: 任务不存在
- `400 Bad Request`: 任务已被接单/已完成，或不能接自己发布的任务，或已经接过此任务
- `401 Unauthorized`: 未认证或token无效
- `403 Forbidden`: 用户未完成基础认证

## 订单相关接口

### 2. 获取订单列表

**接口地址**: `GET /orders`

**功能描述**: 获取用户相关的订单列表

**请求参数**:
- **查询参数**:
  - `page` (int, 可选): 页码，默认1
  - `size` (int, 可选): 每页数量，默认20，最大100
  - `status` (string, 可选): 订单状态筛选
    - `pending`: 待接单
    - `accepted`: 已接单
    - `in_progress`: 进行中
    - `completed`: 已完成
    - `cancelled`: 已取消
  - `role` (string, 可选): 角色筛选
    - `worker`: 只返回作为零工的订单
    - `merchant`: 只返回作为商户的订单

**请求示例**:
```http
GET /api/v1/orders?page=1&size=10&status=in_progress&role=worker
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应格式**:
```json
{
  "orders": [
    {
      "id": 1,
      "task_id": 3,
      "worker_id": 2,
      "merchant_id": 1,
      "status": "in_progress",
      "accepted_at": "2024-01-15T10:30:00Z",
      "confirmed_at": null,
      "started_at": "2024-01-15T10:30:00Z",
      "worker_completed_at": null,
      "merchant_completed_at": null,
      "completed_at": null,
      "worker_rating": null,
      "merchant_rating": null,
      "worker_comment": null,
      "merchant_comment": null,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": null,
      "task_title": "清洁办公室",
      "task_description": "需要清洁办公室，包括地面、桌面等",
      "task_category": "cleaning",
      "task_reward": 100.0,
      "task_address": "北京市朝阳区xxx",
      "task_latitude": 39.9042,
      "task_longitude": 116.4074,
      "worker_nickname": "张三",
      "worker_avatar": "https://example.com/avatar1.jpg",
      "worker_credit_score": 4.5,
      "merchant_nickname": "李四",
      "merchant_avatar": "https://example.com/avatar2.jpg",
      "merchant_credit_score": 4.8
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10
}
```

### 3. 获取订单详情

**接口地址**: `GET /orders/{order_id}`

**功能描述**: 获取指定订单的详细信息

**请求参数**:
- **路径参数**:
  - `order_id` (int): 订单ID

**请求示例**:
```http
GET /api/v1/orders/1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应格式**: 同接单接口的响应格式

**错误响应**:
- `404 Not Found`: 订单不存在
- `403 Forbidden`: 无权查看此订单（只能查看自己相关的订单）

## 订单状态说明

- `pending`: 待接单（任务发布状态）
- `accepted`: 已接单（等待商户确认，仅确认模式）
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

## 任务接单模式说明

- **自动接单模式** (`auto_accept: true`): 接单后直接进入"进行中"状态
- **确认模式** (`auto_accept: false`): 接单后进入"已接单"状态，需要商户确认后才进入"进行中"状态

## 错误码说明

- `200`: 请求成功
- `400`: 请求参数错误或业务逻辑错误
- `401`: 未认证或认证失效
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 请求参数验证失败
- `500`: 服务器内部错误

## 注意事项

1. 所有时间字段均为UTC时间，格式为ISO 8601
2. 用户只能查看与自己相关的订单（作为零工或商户）
3. 接单前会检查任务状态、用户权限等条件
4. 订单创建后会自动更新对应任务的状态
