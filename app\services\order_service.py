from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
from app.models.order import Order, OrderStatus
from app.models.task import Task, TaskStatus
from app.models.user import User


class OrderService:
    """订单相关业务逻辑"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def can_user_accept_task(self, task: Task, user: User) -> Tuple[bool, str]:
        """
        检查用户是否可以接单
        返回 (是否可以, 错误信息)
        """
        # 检查任务状态
        if task.status != TaskStatus.PENDING:
            return False, "任务已被接单或已完成"
        
        # 检查是否是自己发布的任务
        if task.publisher_id == user.id:
            return False, "不能接自己发布的任务"
        
        # 检查是否已经接过这个任务
        existing_order = self.db.query(Order).filter(
            and_(
                Order.task_id == task.id,
                Order.worker_id == user.id,
                Order.status.in_([OrderStatus.ACCEPTED, OrderStatus.IN_PROGRESS])
            )
        ).first()
        
        if existing_order:
            return False, "您已经接过这个任务"
        
        return True, ""
    
    def create_order(self, task: Task, worker: User) -> Order:
        """创建订单"""
        order = Order(
            task_id=task.id,
            worker_id=worker.id,
            merchant_id=task.publisher_id,
            accepted_at=datetime.utcnow()
        )
        
        # 根据任务的auto_accept设置订单状态
        if task.auto_accept:
            order.status = OrderStatus.IN_PROGRESS
            order.started_at = datetime.utcnow()
            task.status = TaskStatus.IN_PROGRESS
        else:
            order.status = OrderStatus.ACCEPTED
            task.status = TaskStatus.ACCEPTED
        
        self.db.add(order)
        self.db.commit()
        self.db.refresh(order)
        
        return order
    
    def confirm_order(self, order: Order, merchant: User) -> bool:
        """商户确认订单"""
        if order.merchant_id != merchant.id:
            return False
        
        if order.status != OrderStatus.ACCEPTED:
            return False
        
        order.status = OrderStatus.IN_PROGRESS
        order.confirmed_at = datetime.utcnow()
        order.started_at = datetime.utcnow()
        order.task.status = TaskStatus.IN_PROGRESS
        
        self.db.commit()
        return True
    
    def cancel_order(self, order: Order, user: User) -> bool:
        """取消订单"""
        if order.worker_id != user.id and order.merchant_id != user.id:
            return False
        
        if order.status == OrderStatus.COMPLETED:
            return False
        
        order.status = OrderStatus.CANCELLED
        order.task.status = TaskStatus.PENDING
        
        self.db.commit()
        return True
    
    def complete_task_by_worker(self, order: Order, worker: User) -> bool:
        """零工标记任务完成"""
        if order.worker_id != worker.id:
            return False
        
        if order.status != OrderStatus.IN_PROGRESS:
            return False
        
        order.worker_completed_at = datetime.utcnow()
        
        # 检查是否双方都确认完成
        if order.merchant_completed_at:
            order.status = OrderStatus.COMPLETED
            order.completed_at = datetime.utcnow()
            order.task.status = TaskStatus.COMPLETED
        
        self.db.commit()
        return True
    
    def complete_task_by_merchant(self, order: Order, merchant: User) -> bool:
        """商户确认任务完成"""
        if order.merchant_id != merchant.id:
            return False
        
        if order.status != OrderStatus.IN_PROGRESS:
            return False
        
        order.merchant_completed_at = datetime.utcnow()
        
        # 检查是否双方都确认完成
        if order.worker_completed_at:
            order.status = OrderStatus.COMPLETED
            order.completed_at = datetime.utcnow()
            order.task.status = TaskStatus.COMPLETED
        
        self.db.commit()
        return True
    
    def rate_worker(self, order: Order, merchant: User, rating: int, comment: str = None) -> bool:
        """商户评价零工"""
        if order.merchant_id != merchant.id:
            return False
        
        if order.status != OrderStatus.COMPLETED:
            return False
        
        if order.worker_rating is not None:
            return False  # 已经评价过了
        
        order.worker_rating = rating
        order.worker_comment = comment
        
        self.db.commit()
        return True
    
    def rate_merchant(self, order: Order, worker: User, rating: int, comment: str = None) -> bool:
        """零工评价商户"""
        if order.worker_id != worker.id:
            return False
        
        if order.status != OrderStatus.COMPLETED:
            return False
        
        if order.merchant_rating is not None:
            return False  # 已经评价过了
        
        order.merchant_rating = rating
        order.merchant_comment = comment
        
        self.db.commit()
        return True
    
    def get_user_orders(
        self,
        user_id: int,
        role: str,  # "worker" or "merchant"
        status: Optional[OrderStatus] = None,
        page: int = 1,
        size: int = 20
    ) -> Tuple[List[Order], int]:
        """获取用户订单"""
        if role == "worker":
            query = self.db.query(Order).filter(Order.worker_id == user_id)
        elif role == "merchant":
            query = self.db.query(Order).filter(Order.merchant_id == user_id)
        else:
            query = self.db.query(Order).filter(
                or_(Order.worker_id == user_id, Order.merchant_id == user_id)
            )
        
        if status:
            query = query.filter(Order.status == status)
        
        total = query.count()
        
        offset = (page - 1) * size
        orders = query.offset(offset).limit(size).all()
        
        return orders, total
    
    def calculate_order_statistics(self, user_id: int) -> dict:
        """计算用户订单统计信息"""
        # 作为零工的订单统计
        worker_stats = self.db.query(Order.status, self.db.func.count(Order.id)).filter(
            Order.worker_id == user_id
        ).group_by(Order.status).all()
        
        # 作为商户的订单统计
        merchant_stats = self.db.query(Order.status, self.db.func.count(Order.id)).filter(
            Order.merchant_id == user_id
        ).group_by(Order.status).all()
        
        worker_count = {status.value: 0 for status in OrderStatus}
        for status, count in worker_stats:
            worker_count[status.value] = count
        
        merchant_count = {status.value: 0 for status in OrderStatus}
        for status, count in merchant_stats:
            merchant_count[status.value] = count
        
        return {
            "as_worker": worker_count,
            "as_merchant": merchant_count,
            "total_as_worker": sum(worker_count.values()),
            "total_as_merchant": sum(merchant_count.values())
        }
