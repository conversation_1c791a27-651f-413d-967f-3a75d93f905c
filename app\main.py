from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import ORJSONResponse
from app.core.config import settings
from app.core.database import engine, Base

# 导入所有模型以确保表被创建
from app.models import user, task, order

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.project_name,
    version="1.0.0",
    description="包工头零工平台API",
    debug=settings.debug,
    default_response_class=ORJSONResponse  # 使用orjson作为默认响应
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境需要限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    return {"message": "包工头零工平台API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


# API路由
from app.api.v1 import auth, tasks, orders
app.include_router(auth.router, prefix=settings.api_v1_str)
app.include_router(tasks.router, prefix=settings.api_v1_str)
app.include_router(orders.router, prefix=settings.api_v1_str)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0", 
        port=8080,
        reload=True
    )

