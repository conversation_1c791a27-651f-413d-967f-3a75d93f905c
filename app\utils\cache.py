import orjson
import pickle
from typing import Any, Optional, Union
from app.core.database import get_redis


class CacheService:
    """Redis缓存服务"""
    
    def __init__(self):
        self.redis = get_redis()
        self.default_expire = 3600  # 默认1小时过期
    
    def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            if expire is None:
                expire = self.default_expire
            
            # 序列化数据
            if isinstance(value, (dict, list)):
                serialized_value = orjson.dumps(value).decode('utf-8')
            elif isinstance(value, (int, float, str, bool)):
                serialized_value = str(value)
            else:
                # 复杂对象使用pickle
                serialized_value = pickle.dumps(value)
            
            return self.redis.setex(key, expire, serialized_value)
        except Exception as e:
            print(f"缓存设置失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存"""
        try:
            value = self.redis.get(key)
            if value is None:
                return default
            
            # 尝试orjson反序列化
            try:
                return orjson.loads(value)
            except (orjson.JSONDecodeError, TypeError):
                pass
            
            # 尝试pickle反序列化
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                pass
            
            # 返回原始字符串
            return value
        except Exception as e:
            print(f"缓存获取失败: {e}")
            return default
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            return bool(self.redis.delete(key))
        except Exception as e:
            print(f"缓存删除失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            return bool(self.redis.exists(key))
        except Exception as e:
            print(f"缓存检查失败: {e}")
            return False
    
    def expire(self, key: str, seconds: int) -> bool:
        """设置缓存过期时间"""
        try:
            return bool(self.redis.expire(key, seconds))
        except Exception as e:
            print(f"设置过期时间失败: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            keys = self.redis.keys(pattern)
            if keys:
                return self.redis.delete(*keys)
            return 0
        except Exception as e:
            print(f"批量删除缓存失败: {e}")
            return 0


class TaskCacheService:
    """任务相关缓存服务"""
    
    def __init__(self):
        self.cache = CacheService()
        self.task_expire = 300  # 任务缓存5分钟
        self.list_expire = 60   # 列表缓存1分钟
    
    def get_task_cache_key(self, task_id: int) -> str:
        return f"task:{task_id}"
    
    def get_nearby_tasks_cache_key(
        self,
        lat: float,
        lon: float,
        distance: float,
        category: Optional[str] = None
    ) -> str:
        """生成附近任务缓存键"""
        key_parts = [f"nearby_tasks", f"{lat:.4f}", f"{lon:.4f}", f"{distance}"]
        if category:
            key_parts.append(category)
        return ":".join(key_parts)
    
    def cache_task(self, task_id: int, task_data: dict) -> bool:
        """缓存任务数据"""
        key = self.get_task_cache_key(task_id)
        return self.cache.set(key, task_data, self.task_expire)
    
    def get_cached_task(self, task_id: int) -> Optional[dict]:
        """获取缓存的任务数据"""
        key = self.get_task_cache_key(task_id)
        return self.cache.get(key)
    
    def cache_nearby_tasks(
        self,
        lat: float,
        lon: float,
        distance: float,
        tasks_data: list,
        category: Optional[str] = None
    ) -> bool:
        """缓存附近任务列表"""
        key = self.get_nearby_tasks_cache_key(lat, lon, distance, category)
        return self.cache.set(key, tasks_data, self.list_expire)
    
    def get_cached_nearby_tasks(
        self,
        lat: float,
        lon: float,
        distance: float,
        category: Optional[str] = None
    ) -> Optional[list]:
        """获取缓存的附近任务列表"""
        key = self.get_nearby_tasks_cache_key(lat, lon, distance, category)
        return self.cache.get(key)
    
    def invalidate_task_cache(self, task_id: int) -> bool:
        """使任务缓存失效"""
        key = self.get_task_cache_key(task_id)
        return self.cache.delete(key)
    
    def invalidate_nearby_tasks_cache(self) -> int:
        """使所有附近任务缓存失效"""
        return self.cache.clear_pattern("nearby_tasks:*")


class UserCacheService:
    """用户相关缓存服务"""
    
    def __init__(self):
        self.cache = CacheService()
        self.user_expire = 1800  # 用户缓存30分钟
    
    def get_user_cache_key(self, user_id: int) -> str:
        return f"user:{user_id}"
    
    def get_user_stats_cache_key(self, user_id: int) -> str:
        return f"user_stats:{user_id}"
    
    def cache_user(self, user_id: int, user_data: dict) -> bool:
        """缓存用户数据"""
        key = self.get_user_cache_key(user_id)
        return self.cache.set(key, user_data, self.user_expire)
    
    def get_cached_user(self, user_id: int) -> Optional[dict]:
        """获取缓存的用户数据"""
        key = self.get_user_cache_key(user_id)
        return self.cache.get(key)
    
    def cache_user_stats(self, user_id: int, stats_data: dict) -> bool:
        """缓存用户统计数据"""
        key = self.get_user_stats_cache_key(user_id)
        return self.cache.set(key, stats_data, 600)  # 10分钟
    
    def get_cached_user_stats(self, user_id: int) -> Optional[dict]:
        """获取缓存的用户统计数据"""
        key = self.get_user_stats_cache_key(user_id)
        return self.cache.get(key)
    
    def invalidate_user_cache(self, user_id: int) -> bool:
        """使用户缓存失效"""
        user_key = self.get_user_cache_key(user_id)
        stats_key = self.get_user_stats_cache_key(user_id)
        return self.cache.delete(user_key) and self.cache.delete(stats_key)


# 全局缓存服务实例
cache_service = CacheService()
task_cache = TaskCacheService()
user_cache = UserCacheService()
