from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, Enum, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class OrderStatus(enum.Enum):
    PENDING = "pending"         # 待接单（任务发布状态）
    ACCEPTED = "accepted"       # 已接单（等待商户确认，仅确认模式）
    IN_PROGRESS = "in_progress" # 进行中
    COMPLETED = "completed"     # 已完成
    CANCELLED = "cancelled"     # 已取消


class Order(Base):
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    worker_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    merchant_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # 订单状态
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)

    # 时间记录
    accepted_at = Column(DateTime(timezone=True))    # 接单时间
    confirmed_at = Column(DateTime(timezone=True))   # 商户确认时间（仅确认模式）
    started_at = Column(DateTime(timezone=True))     # 开始工作时间
    worker_completed_at = Column(DateTime(timezone=True))   # 零工标记完成时间
    merchant_completed_at = Column(DateTime(timezone=True)) # 商户确认完成时间
    completed_at = Column(DateTime(timezone=True))   # 最终完成时间

    # 评价信息
    worker_rating = Column(Integer)  # 商户对零工的评分 1-5
    merchant_rating = Column(Integer)  # 零工对商户的评分 1-5
    worker_comment = Column(Text)    # 商户对零工的评价
    merchant_comment = Column(Text)  # 零工对商户的评价

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    task = relationship("Task", back_populates="orders")
    worker = relationship("User", foreign_keys=[worker_id])
    merchant = relationship("User", foreign_keys=[merchant_id])
