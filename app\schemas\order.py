from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.order import OrderStatus


class OrderBase(BaseModel):
    task_id: int
    worker_id: int
    merchant_id: int


class OrderCreate(BaseModel):
    task_id: int = Field(..., description="任务ID")


class OrderUpdate(BaseModel):
    status: Optional[OrderStatus] = None


class OrderResponse(BaseModel):
    id: int
    task_id: int
    worker_id: int
    merchant_id: int
    status: OrderStatus
    
    # 时间记录
    accepted_at: Optional[datetime] = None
    confirmed_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    worker_completed_at: Optional[datetime] = None
    merchant_completed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 评价信息
    worker_rating: Optional[int] = Field(None, ge=1, le=5)
    merchant_rating: Optional[int] = Field(None, ge=1, le=5)
    worker_comment: Optional[str] = None
    merchant_comment: Optional[str] = None
    
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class OrderDetailResponse(OrderResponse):
    # 任务信息
    task_title: str
    task_description: str
    task_category: str
    task_reward: float
    task_address: str
    task_latitude: float
    task_longitude: float
    
    # 用户信息
    worker_nickname: str
    worker_avatar: Optional[str]
    worker_credit_score: float
    merchant_nickname: str
    merchant_avatar: Optional[str]
    merchant_credit_score: float


class OrderListResponse(BaseModel):
    orders: List[OrderDetailResponse]
    total: int
    page: int
    size: int


# 订单查询参数
class OrderQueryParams(BaseModel):
    page: int = Field(1, ge=1)
    size: int = Field(20, ge=1, le=100)
    status: Optional[OrderStatus] = None
    worker_id: Optional[int] = None
    merchant_id: Optional[int] = None
    task_id: Optional[int] = None


# 订单状态更新
class OrderStatusUpdate(BaseModel):
    order_id: int = Field(..., description="订单ID")
    status: OrderStatus


# 任务完成确认
class TaskCompleteRequest(BaseModel):
    role: str = Field(..., pattern=r'^(worker|merchant)$', description="完成确认角色")


# 评价相关
class OrderRatingRequest(BaseModel):
    rating: int = Field(..., ge=1, le=5, description="评分1-5星")
    comment: Optional[str] = Field(None, max_length=500, description="评价内容")
    role: str = Field(..., pattern=r'^(worker|merchant)$', description="评价角色")
