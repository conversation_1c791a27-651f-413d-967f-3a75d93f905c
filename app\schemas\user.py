from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from app.models.user import UserType, VerificationLevel


class UserBase(BaseModel):
    nickname: str = Field(..., min_length=1, max_length=50)
    avatar_url: Optional[str] = None
    phone: Optional[str] = Field(None, pattern=r'^1[3-9]\d{9}$')
    user_type: UserType = UserType.WORKER
    real_name: Optional[str] = Field(None, max_length=20)
    id_card: Optional[str] = Field(None, pattern=r'^\d{17}[\dXx]$')
    address: Optional[str] = Field(None, max_length=200)
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)


class UserCreate(UserBase):
    openid: str = Field(..., min_length=1, max_length=100)


class UserUpdate(BaseModel):
    nickname: Optional[str] = Field(None, min_length=1, max_length=50)
    avatar_url: Optional[str] = None
    phone: Optional[str] = Field(None, pattern=r'^1[3-9]\d{9}$')
    user_type: Optional[UserType] = None
    real_name: Optional[str] = Field(None, max_length=20)
    id_card: Optional[str] = Field(None, pattern=r'^\d{17}[\dXx]$')
    address: Optional[str] = Field(None, max_length=200)
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)


class UserResponse(UserBase):
    id: int
    openid: str
    verification_level: VerificationLevel
    is_verified: bool
    credit_score: float
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class UserProfile(BaseModel):
    id: int
    nickname: str
    avatar_url: Optional[str]
    user_type: UserType
    verification_level: VerificationLevel
    credit_score: float
    phone: Optional[str]
    real_name: Optional[str]
    address: Optional[str]

    class Config:
        from_attributes = True


# 微信登录相关
class WechatLoginRequest(BaseModel):
    code: str = Field(..., description="微信授权码")
    nickname: str = Field(..., min_length=1, max_length=50)
    avatar_url: Optional[str] = None


class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: UserProfile


# 手机号验证
class PhoneVerificationRequest(BaseModel):
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$')


class PhoneVerificationConfirm(BaseModel):
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$')
    code: str = Field(..., min_length=4, max_length=6)
