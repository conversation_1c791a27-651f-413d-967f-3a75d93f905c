from celery import Celery
from app.core.config import settings

# 创建Celery应用
celery_app = Celery(
    "baogongtou",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.tasks.notification_tasks", "app.tasks.user_tasks"]
)

# Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    # 使用orjson进行序列化（需要自定义序列化器）
    task_compression='gzip',  # 启用压缩提升性能
)

# 任务路由配置
celery_app.conf.task_routes = {
    "app.tasks.notification_tasks.*": {"queue": "notifications"},
    "app.tasks.user_tasks.*": {"queue": "users"},
}
