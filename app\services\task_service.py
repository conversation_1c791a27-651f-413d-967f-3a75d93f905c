from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.task import Task, TaskStatus
from app.models.user import User
from app.utils.geo import calculate_distance, get_bounding_box
from app.schemas.task import TaskResponse


class TaskService:
    """任务相关业务逻辑"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_nearby_tasks_with_distance(
        self,
        user_lat: float,
        user_lon: float,
        max_distance: float,
        category: Optional[str] = None,
        min_reward: Optional[float] = None,
        max_reward: Optional[float] = None,
        limit: int = 50
    ) -> List[Tuple[Task, float]]:
        """
        获取附近任务并计算距离
        返回 (Task, distance) 的列表
        """
        # 计算边界框以优化数据库查询
        min_lat, max_lat, min_lon, max_lon = get_bounding_box(
            user_lat, user_lon, max_distance
        )
        
        # 构建查询
        query = self.db.query(Task).filter(
            and_(
                Task.status == TaskStatus.PENDING,
                Task.latitude.between(min_lat, max_lat),
                Task.longitude.between(min_lon, max_lon)
            )
        )
        
        # 应用筛选条件
        if category:
            query = query.filter(Task.category == category)
        if min_reward is not None:
            query = query.filter(Task.reward >= min_reward)
        if max_reward is not None:
            query = query.filter(Task.reward <= max_reward)
        
        # 获取候选任务
        candidate_tasks = query.limit(limit * 2).all()  # 获取更多候选以确保有足够的结果
        
        # 精确计算距离并筛选
        tasks_with_distance = []
        for task in candidate_tasks:
            distance = calculate_distance(
                user_lat, user_lon,
                task.latitude, task.longitude
            )
            if distance <= max_distance:
                tasks_with_distance.append((task, distance))
        
        # 按距离排序并限制数量
        tasks_with_distance.sort(key=lambda x: x[1])
        return tasks_with_distance[:limit]
    
    def can_user_modify_task(self, task: Task, user: User) -> bool:
        """检查用户是否可以修改任务"""
        return (
            task.publisher_id == user.id and 
            task.status == TaskStatus.PENDING
        )
    
    def can_user_delete_task(self, task: Task, user: User) -> bool:
        """检查用户是否可以删除任务"""
        return (
            task.publisher_id == user.id and 
            task.status in [TaskStatus.PENDING, TaskStatus.CANCELLED]
        )
    
    def get_user_published_tasks(
        self,
        user_id: int,
        status: Optional[TaskStatus] = None,
        page: int = 1,
        size: int = 20
    ) -> Tuple[List[Task], int]:
        """获取用户发布的任务"""
        query = self.db.query(Task).filter(Task.publisher_id == user_id)
        
        if status:
            query = query.filter(Task.status == status)
        
        total = query.count()
        
        offset = (page - 1) * size
        tasks = query.offset(offset).limit(size).all()
        
        return tasks, total
    
    def calculate_task_statistics(self, user_id: int) -> dict:
        """计算用户任务统计信息"""
        # 发布的任务统计
        published_stats = self.db.query(Task.status, self.db.func.count(Task.id)).filter(
            Task.publisher_id == user_id
        ).group_by(Task.status).all()
        
        published_count = {status.value: 0 for status in TaskStatus}
        for status, count in published_stats:
            published_count[status.value] = count
        
        return {
            "published_tasks": published_count,
            "total_published": sum(published_count.values())
        }
    
    def get_recommended_tasks(
        self,
        user: User,
        limit: int = 10
    ) -> List[Task]:
        """
        为用户推荐任务
        基于用户位置、历史接单记录等
        """
        if not user.latitude or not user.longitude:
            # 如果用户没有位置信息，返回最新的任务
            return self.db.query(Task).filter(
                Task.status == TaskStatus.PENDING
            ).order_by(Task.created_at.desc()).limit(limit).all()
        
        # 获取附近的任务
        nearby_tasks = self.get_nearby_tasks_with_distance(
            user.latitude, user.longitude,
            max_distance=20.0,  # 20公里范围内
            limit=limit
        )
        
        return [task for task, distance in nearby_tasks]
