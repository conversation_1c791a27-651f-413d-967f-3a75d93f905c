from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_
from typing import Optional
from datetime import datetime
from app.core.database import get_db
from app.models.user import User
from app.models.task import Task, TaskStatus
from app.models.order import Order, OrderStatus
from app.schemas.order import (
    OrderCreate, OrderResponse, OrderDetailResponse, OrderListResponse,
    OrderQueryParams, OrderStatusUpdate, TaskCompleteRequest, OrderRatingRequest
)
from app.utils.auth import get_current_user, require_verification
from app.tasks.notification_tasks import (
    send_task_accepted_notification, send_order_status_notification,
    send_task_completed_notification
)
from app.tasks.user_tasks import update_user_credit_score

router = APIRouter(prefix="/orders", tags=["订单管理"])


@router.post("", response_model=OrderDetailResponse)
async def create_order(
    order_data: OrderCreate,
    current_user: User = Depends(require_verification("basic")),
    db: Session = Depends(get_db)
):
    """接单"""
    # 获取任务信息
    task = db.query(Task).filter(Task.id == order_data.task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )
    
    # 检查任务状态
    if task.status != TaskStatus.PENDING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务已被接单或已完成"
        )
    
    # 检查是否是自己发布的任务
    if task.publisher_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能接自己发布的任务"
        )
    
    # 检查是否已经接过这个任务
    existing_order = db.query(Order).filter(
        and_(
            Order.task_id == order_data.task_id,
            Order.worker_id == current_user.id,
            Order.status.in_([OrderStatus.ACCEPTED, OrderStatus.IN_PROGRESS])
        )
    ).first()
    
    if existing_order:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="您已经接过这个任务"
        )
    
    # 创建订单
    order = Order(
        task_id=order_data.task_id,
        worker_id=current_user.id,
        merchant_id=task.publisher_id,
        accepted_at=datetime.utcnow()
    )
    
    # 根据任务的auto_accept设置订单状态
    if task.auto_accept:
        order.status = OrderStatus.IN_PROGRESS
        order.started_at = datetime.utcnow()
        task.status = TaskStatus.IN_PROGRESS
    else:
        order.status = OrderStatus.ACCEPTED
        task.status = TaskStatus.ACCEPTED
    
    db.add(order)
    db.commit()
    db.refresh(order)

    # 发送接单通知
    send_task_accepted_notification.delay(task.id, current_user.id)

    # 构造响应数据
    return _build_order_detail_response(order, db)


@router.get("", response_model=OrderListResponse)
async def get_orders(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[OrderStatus] = None,
    role: Optional[str] = Query(None, regex=r'^(worker|merchant)$'),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取订单列表"""
    query = db.query(Order).options(
        joinedload(Order.task),
        joinedload(Order.worker),
        joinedload(Order.merchant)
    )
    
    # 根据角色筛选
    if role == "worker":
        query = query.filter(Order.worker_id == current_user.id)
    elif role == "merchant":
        query = query.filter(Order.merchant_id == current_user.id)
    else:
        # 默认返回用户相关的所有订单
        query = query.filter(
            or_(
                Order.worker_id == current_user.id,
                Order.merchant_id == current_user.id
            )
        )
    
    # 状态筛选
    if status:
        query = query.filter(Order.status == status)
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    orders = query.offset(offset).limit(size).all()
    
    # 构造响应数据
    order_responses = [_build_order_detail_response(order, db) for order in orders]
    
    return OrderListResponse(
        orders=order_responses,
        total=total,
        page=page,
        size=size
    )


@router.get("/{order_id}", response_model=OrderDetailResponse)
async def get_order(
    order_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取订单详情"""
    order = db.query(Order).options(
        joinedload(Order.task),
        joinedload(Order.worker),
        joinedload(Order.merchant)
    ).filter(Order.id == order_id).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    # 检查权限
    if order.worker_id != current_user.id and order.merchant_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权查看此订单"
        )
    
    return _build_order_detail_response(order, db)


@router.post("/update-status", response_model=OrderDetailResponse)
async def update_order_status(
    status_data: OrderStatusUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新订单状态"""
    order = db.query(Order).options(
        joinedload(Order.task),
        joinedload(Order.worker),
        joinedload(Order.merchant)
    ).filter(Order.id == status_data.order_id).first()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )

    # 检查权限和状态流转逻辑
    if status_data.status == OrderStatus.IN_PROGRESS:
        # 商户确认接单
        if order.merchant_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有商户可以确认接单"
            )
        if order.status != OrderStatus.ACCEPTED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="订单状态不正确"
            )
        order.confirmed_at = datetime.utcnow()
        order.started_at = datetime.utcnow()
        order.task.status = TaskStatus.IN_PROGRESS

    elif status_data.status == OrderStatus.CANCELLED:
        # 取消订单
        if order.worker_id != current_user.id and order.merchant_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权取消此订单"
            )
        if order.status == OrderStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="已完成的订单不能取消"
            )
        order.task.status = TaskStatus.PENDING

    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的状态更新"
        )

    order.status = status_data.status
    db.commit()

    # 发送状态变更通知
    send_order_status_notification.delay(order.id, status_data.status.value)

    return _build_order_detail_response(order, db)


@router.post("/complete")
async def complete_task(
    order_id: int,
    complete_data: TaskCompleteRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """任务完成确认"""
    order = db.query(Order).options(
        joinedload(Order.task)
    ).filter(Order.id == order_id).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    if order.status != OrderStatus.IN_PROGRESS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="订单状态不正确"
        )
    
    now = datetime.utcnow()
    
    if complete_data.role == "worker":
        # 零工标记完成
        if order.worker_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有接单零工可以标记完成"
            )
        order.worker_completed_at = now
        message = "任务已标记完成，等待商户确认"
    
    elif complete_data.role == "merchant":
        # 商户确认完成
        if order.merchant_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有发布商户可以确认完成"
            )
        order.merchant_completed_at = now
        message = "任务完成已确认"
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的角色"
        )
    
    # 检查是否双方都确认完成
    if order.worker_completed_at and order.merchant_completed_at:
        order.status = OrderStatus.COMPLETED
        order.completed_at = now
        order.task.status = TaskStatus.COMPLETED
        message = "任务已完成"

        # 发送任务完成通知
        send_task_completed_notification.delay(order.id)

        # 异步更新双方信用评分
        update_user_credit_score.delay(order.worker_id)
        update_user_credit_score.delay(order.merchant_id)

    db.commit()

    return {"message": message}


@router.post("/rating")
async def rate_order(
    order_id: int,
    rating_data: OrderRatingRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """订单评价"""
    order = db.query(Order).filter(Order.id == order_id).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    if order.status != OrderStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能对已完成的订单进行评价"
        )
    
    if rating_data.role == "merchant":
        # 商户评价零工
        if order.merchant_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有发布商户可以评价零工"
            )
        if order.worker_rating is not None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="已经评价过了"
            )
        order.worker_rating = rating_data.rating
        order.worker_comment = rating_data.comment
    
    elif rating_data.role == "worker":
        # 零工评价商户
        if order.worker_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有接单零工可以评价商户"
            )
        if order.merchant_rating is not None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="已经评价过了"
            )
        order.merchant_rating = rating_data.rating
        order.merchant_comment = rating_data.comment
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的角色"
        )
    
    db.commit()
    
    return {"message": "评价成功"}


def _build_order_detail_response(order: Order, db: Session) -> OrderDetailResponse:
    """构建订单详情响应"""
    return OrderDetailResponse(
        id=order.id,
        task_id=order.task_id,
        worker_id=order.worker_id,
        merchant_id=order.merchant_id,
        status=order.status,
        accepted_at=order.accepted_at,
        confirmed_at=order.confirmed_at,
        started_at=order.started_at,
        worker_completed_at=order.worker_completed_at,
        merchant_completed_at=order.merchant_completed_at,
        completed_at=order.completed_at,
        worker_rating=order.worker_rating,
        merchant_rating=order.merchant_rating,
        worker_comment=order.worker_comment,
        merchant_comment=order.merchant_comment,
        created_at=order.created_at,
        updated_at=order.updated_at,
        # 任务信息
        task_title=order.task.title,
        task_description=order.task.description,
        task_category=order.task.category.value,
        task_reward=order.task.reward,
        task_address=order.task.address,
        task_latitude=order.task.latitude,
        task_longitude=order.task.longitude,
        # 用户信息
        worker_nickname=order.worker.nickname,
        worker_avatar=order.worker.avatar_url,
        worker_credit_score=order.worker.credit_score,
        merchant_nickname=order.merchant.nickname,
        merchant_avatar=order.merchant.avatar_url,
        merchant_credit_score=order.merchant.credit_score,
    )
