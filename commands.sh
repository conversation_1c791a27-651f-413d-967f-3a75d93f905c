# 修改Docker配置，只保留可用的源
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn"
  ]
}
EOF

# 重启Docker服务
sudo systemctl restart docker

# 测试拉取镜像
docker pull redis:7-alpine
docker pull postgres:15

# 成功后运行容器
docker run -d --name redis -p 6379:6379 -v redis_data:/data redis:7-alpine
docker run -d --name postgres -e POSTGRES_DB=baogongtou -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=password -p 5432:5432 -v postgres_data:/var/lib/postgresql/data postgres:15