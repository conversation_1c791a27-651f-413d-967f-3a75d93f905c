from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # 数据库配置
    database_url: str
    redis_url: str
    
    # JWT配置
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 微信小程序配置
    wechat_app_id: str
    wechat_app_secret: str
    
    # 环境配置
    environment: str = "development"
    debug: bool = True
    skip_wechat_validation: bool = False
    
    # Celery配置
    celery_broker_url: str
    celery_result_backend: str
    
    # API配置
    api_v1_str: str = "/api/v1"
    project_name: str = "包工头零工平台"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
