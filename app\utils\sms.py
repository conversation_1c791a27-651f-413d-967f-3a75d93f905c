import random
import string
from typing import Optional
from app.core.database import get_redis


class SMSService:
    """短信验证码服务"""
    
    def __init__(self):
        self.redis = get_redis()
        self.code_expire = 300  # 验证码5分钟过期
        self.code_length = 6
    
    def generate_code(self) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=self.code_length))
    
    async def send_verification_code(self, phone: str) -> bool:
        """
        发送验证码
        第一阶段：模拟发送，实际存储到Redis
        """
        # 检查发送频率限制（1分钟内只能发送一次）
        rate_limit_key = f"sms_rate_limit:{phone}"
        if self.redis.exists(rate_limit_key):
            return False
        
        # 生成验证码
        code = self.generate_code()
        
        # 存储验证码到Redis
        code_key = f"sms_code:{phone}"
        self.redis.setex(code_key, self.code_expire, code)
        
        # 设置发送频率限制
        self.redis.setex(rate_limit_key, 60, "1")
        
        # 第一阶段：打印验证码（实际应该调用短信服务商API）
        print(f"发送验证码到 {phone}: {code}")
        
        return True
    
    def verify_code(self, phone: str, code: str) -> bool:
        """验证验证码"""
        code_key = f"sms_code:{phone}"
        stored_code = self.redis.get(code_key)
        
        if not stored_code:
            return False
        
        if stored_code == code:
            # 验证成功后删除验证码
            self.redis.delete(code_key)
            return True
        
        return False
    
    def clear_rate_limit(self, phone: str):
        """清除发送频率限制（测试用）"""
        rate_limit_key = f"sms_rate_limit:{phone}"
        self.redis.delete(rate_limit_key)


# 全局实例
sms_service = SMSService()
