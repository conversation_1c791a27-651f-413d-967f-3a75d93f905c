import httpx
from typing import Optional, Dict, Any
from app.core.config import settings


class WechatAPI:
    """微信小程序API工具类"""
    
    def __init__(self):
        self.app_id = settings.wechat_app_id
        self.app_secret = settings.wechat_app_secret
        self.base_url = "https://api.weixin.qq.com"
    
    async def code_to_session(self, code: str) -> Optional[Dict[str, Any]]:
        """
        通过code获取openid和session_key
        """
        url = f"{self.base_url}/sns/jscode2session"
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "errcode" in data:
                    print(f"微信API错误: {data}")
                    return None
                
                return data
            except Exception as e:
                print(f"微信API请求失败: {e}")
                return None
    
    async def get_access_token(self) -> Optional[str]:
        """
        获取小程序全局access_token
        """
        url = f"{self.base_url}/cgi-bin/token"
        params = {
            "grant_type": "client_credential",
            "appid": self.app_id,
            "secret": self.app_secret
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "errcode" in data:
                    print(f"获取access_token失败: {data}")
                    return None
                
                return data.get("access_token")
            except Exception as e:
                print(f"获取access_token请求失败: {e}")
                return None


# 全局实例
wechat_api = WechatAPI()
