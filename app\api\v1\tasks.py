from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_
from typing import Optional, List
from app.core.database import get_db
from app.models.user import User
from app.models.task import Task, TaskStatus
from app.models.order import Order, OrderStatus
from app.schemas.task import (
    TaskCreate, TaskUpdate, TaskDelete, TaskResponse, TaskListResponse,
    TaskQueryParams, NearbyTasksRequest
)
from app.schemas.order import OrderCreate, OrderDetailResponse
from app.utils.auth import get_current_user, require_verification
from app.utils.geo import calculate_distance, get_bounding_box
from app.utils.cache import task_cache

router = APIRouter(prefix="/tasks", tags=["任务管理"])


@router.post("", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(require_verification("basic")),
    db: Session = Depends(get_db)
):
    """发布任务"""
    task = Task(
        publisher_id=current_user.id,
        **task_data.dict()
    )

    db.add(task)
    db.commit()
    db.refresh(task)

    # 构造响应数据
    response_data = TaskResponse.from_orm(task)
    response_data.publisher_nickname = current_user.nickname
    response_data.publisher_avatar = current_user.avatar_url
    response_data.publisher_credit_score = current_user.credit_score

    # 清除附近任务缓存
    task_cache.invalidate_nearby_tasks_cache()

    return response_data


@router.get("", response_model=TaskListResponse)
async def get_tasks(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    category: Optional[str] = None,
    min_reward: Optional[float] = Query(None, ge=0),
    max_reward: Optional[float] = Query(None, ge=0),
    status: Optional[TaskStatus] = None,
    publisher_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务列表"""
    query = db.query(Task).options(joinedload(Task.publisher))
    
    # 应用筛选条件
    if category:
        query = query.filter(Task.category == category)
    if min_reward is not None:
        query = query.filter(Task.reward >= min_reward)
    if max_reward is not None:
        query = query.filter(Task.reward <= max_reward)
    if status:
        query = query.filter(Task.status == status)
    if publisher_id:
        query = query.filter(Task.publisher_id == publisher_id)
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    tasks = query.offset(offset).limit(size).all()
    
    # 构造响应数据
    task_responses = []
    for task in tasks:
        response_data = TaskResponse.from_orm(task)
        response_data.publisher_nickname = task.publisher.nickname
        response_data.publisher_avatar = task.publisher.avatar_url
        response_data.publisher_credit_score = task.publisher.credit_score
        
        # 如果用户有位置信息，计算距离
        if current_user.latitude and current_user.longitude:
            response_data.distance = calculate_distance(
                current_user.latitude, current_user.longitude,
                task.latitude, task.longitude
            )
        
        task_responses.append(response_data)
    
    return TaskListResponse(
        tasks=task_responses,
        total=total,
        page=page,
        size=size
    )


@router.post("/nearby", response_model=TaskListResponse)
async def get_nearby_tasks(
    request: NearbyTasksRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取附近任务（零工端）"""
    # 尝试从缓存获取
    cached_tasks = task_cache.get_cached_nearby_tasks(
        request.latitude, request.longitude, request.max_distance,
        request.category.value if request.category else None
    )

    if cached_tasks is not None:
        # 从缓存中分页
        total = len(cached_tasks)
        start = (request.page - 1) * request.size
        end = start + request.size
        paginated_tasks = cached_tasks[start:end]

        return TaskListResponse(
            tasks=paginated_tasks,
            total=total,
            page=request.page,
            size=request.size
        )

    # 缓存未命中，从数据库查询
    # 计算边界框以优化查询
    min_lat, max_lat, min_lon, max_lon = get_bounding_box(
        request.latitude, request.longitude, request.max_distance
    )

    # 构建查询
    query = db.query(Task).options(joinedload(Task.publisher)).filter(
        and_(
            Task.status == TaskStatus.PENDING,
            Task.latitude.between(min_lat, max_lat),
            Task.longitude.between(min_lon, max_lon)
        )
    )

    # 应用筛选条件
    if request.category:
        query = query.filter(Task.category == request.category)
    if request.min_reward is not None:
        query = query.filter(Task.reward >= request.min_reward)
    if request.max_reward is not None:
        query = query.filter(Task.reward <= request.max_reward)

    # 获取所有符合条件的任务
    all_tasks = query.all()

    # 精确计算距离并筛选
    nearby_tasks = []
    for task in all_tasks:
        distance = calculate_distance(
            request.latitude, request.longitude,
            task.latitude, task.longitude
        )
        if distance <= request.max_distance:
            response_data = TaskResponse.from_orm(task)
            response_data.publisher_nickname = task.publisher.nickname
            response_data.publisher_avatar = task.publisher.avatar_url
            response_data.publisher_credit_score = task.publisher.credit_score
            response_data.distance = distance
            nearby_tasks.append(response_data)

    # 按距离排序
    nearby_tasks.sort(key=lambda x: x.distance or float('inf'))

    # 缓存结果（转换为字典格式）
    tasks_dict = [task.dict() for task in nearby_tasks]
    task_cache.cache_nearby_tasks(
        request.latitude, request.longitude, request.max_distance,
        tasks_dict, request.category.value if request.category else None
    )

    # 分页
    total = len(nearby_tasks)
    start = (request.page - 1) * request.size
    end = start + request.size
    paginated_tasks = nearby_tasks[start:end]

    return TaskListResponse(
        tasks=paginated_tasks,
        total=total,
        page=request.page,
        size=request.size
    )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务详情"""
    task = db.query(Task).options(joinedload(Task.publisher)).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )
    
    response_data = TaskResponse.from_orm(task)
    response_data.publisher_nickname = task.publisher.nickname
    response_data.publisher_avatar = task.publisher.avatar_url
    response_data.publisher_credit_score = task.publisher.credit_score
    
    # 计算距离
    if current_user.latitude and current_user.longitude:
        response_data.distance = calculate_distance(
            current_user.latitude, current_user.longitude,
            task.latitude, task.longitude
        )
    
    return response_data


@router.post("/update", response_model=TaskResponse)
async def update_task(
    task_data: TaskUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新任务"""
    task = db.query(Task).filter(Task.id == task_data.task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 检查权限
    if task.publisher_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只能修改自己发布的任务"
        )

    # 检查任务状态
    if task.status != TaskStatus.PENDING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能修改待接单状态的任务"
        )

    # 更新任务
    update_data = task_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(task, field, value)

    db.commit()
    db.refresh(task)

    response_data = TaskResponse.from_orm(task)
    response_data.publisher_nickname = current_user.nickname
    response_data.publisher_avatar = current_user.avatar_url
    response_data.publisher_credit_score = current_user.credit_score

    return response_data


@router.post("/delete")
async def delete_task(
    task_data: TaskDelete,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除任务"""
    task = db.query(Task).filter(Task.id == task_data.task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 检查权限
    if task.publisher_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只能删除自己发布的任务"
        )

    # 检查任务状态
    if task.status not in [TaskStatus.PENDING, TaskStatus.CANCELLED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能删除待接单或已取消状态的任务"
        )

    db.delete(task)
    db.commit()

    return {"message": "任务删除成功"}


@router.post("/{task_id}/accept", response_model=OrderDetailResponse)
async def accept_task(
    task_id: int,
    current_user: User = Depends(require_verification("basic")),
    db: Session = Depends(get_db)
):
    """接单"""
    from datetime import datetime

    # 获取任务信息
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 检查任务状态
    if task.status != TaskStatus.PENDING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务已被接单或已完成"
        )

    # 检查是否是自己发布的任务
    if task.publisher_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能接自己发布的任务"
        )

    # 检查是否已经接过这个任务
    from sqlalchemy import and_
    existing_order = db.query(Order).filter(
        and_(
            Order.task_id == task_id,
            Order.worker_id == current_user.id,
            Order.status.in_([OrderStatus.ACCEPTED, OrderStatus.IN_PROGRESS])
        )
    ).first()

    if existing_order:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="您已经接过这个任务"
        )

    # 创建订单
    order = Order(
        task_id=task_id,
        worker_id=current_user.id,
        merchant_id=task.publisher_id,
        accepted_at=datetime.utcnow()
    )

    # 根据任务的auto_accept设置订单状态
    if task.auto_accept:
        order.status = OrderStatus.IN_PROGRESS
        order.started_at = datetime.utcnow()
        task.status = TaskStatus.IN_PROGRESS
    else:
        order.status = OrderStatus.ACCEPTED
        task.status = TaskStatus.ACCEPTED

    db.add(order)
    db.commit()
    db.refresh(order)

    # 加载关联数据
    order = db.query(Order).options(
        joinedload(Order.task),
        joinedload(Order.worker),
        joinedload(Order.merchant)
    ).filter(Order.id == order.id).first()

    # 构建响应
    from app.api.v1.orders import _build_order_detail_response
    return _build_order_detail_response(order, db)
