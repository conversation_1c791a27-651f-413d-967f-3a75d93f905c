from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import create_access_token
from app.core.config import settings
from app.models.user import User, VerificationLevel
from app.schemas.user import (
    WechatLoginRequest, LoginResponse, UserProfile, UserUpdate,
    PhoneVerificationRequest, PhoneVerificationConfirm
)
from app.utils.wechat import wechat_api
from app.utils.sms import sms_service
from app.utils.auth import get_current_user
from app.utils.cache import user_cache
from datetime import timedelta

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/wechat-login", response_model=LoginResponse)
async def wechat_login(
    login_data: WechatLoginRequest,
    db: Session = Depends(get_db)
):
    """微信小程序登录"""
    # 开发环境跳过微信验证
    if settings.skip_wechat_validation:
        # 使用code作为openid进行测试
        openid = login_data.code
    else:
        # 通过code获取openid
        wechat_data = await wechat_api.code_to_session(login_data.code)
        if not wechat_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="微信授权失败"
            )

        openid = wechat_data.get("openid")
        if not openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="获取微信用户信息失败"
            )
    
    # 查找或创建用户
    user = db.query(User).filter(User.openid == openid).first()
    if not user:
        user = User(
            openid=openid,
            nickname=login_data.nickname,
            avatar_url=login_data.avatar_url,
            verification_level=VerificationLevel.NONE
        )
        db.add(user)
        db.commit()
        db.refresh(user)
    else:
        # 更新用户信息
        user.nickname = login_data.nickname
        if login_data.avatar_url:
            user.avatar_url = login_data.avatar_url
        db.commit()
    
    # 生成访问令牌
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=timedelta(days=7)  # 7天有效期
    )
    
    return LoginResponse(
        access_token=access_token,
        user=UserProfile.from_orm(user)
    )


@router.get("/profile", response_model=UserProfile)
async def get_profile(current_user: User = Depends(get_current_user)):
    """获取用户信息"""
    return UserProfile.from_orm(current_user)


@router.post("/update-profile", response_model=UserProfile)
async def update_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    update_data = user_data.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(current_user, field, value)

    db.commit()
    db.refresh(current_user)

    # 清除用户缓存
    user_cache.invalidate_user_cache(current_user.id)

    return UserProfile.from_orm(current_user)


@router.post("/send-sms")
async def send_sms_code(
    request: PhoneVerificationRequest,
    current_user: User = Depends(get_current_user)
):
    """发送手机验证码"""
    success = await sms_service.send_verification_code(request.phone)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="发送过于频繁，请稍后再试"
        )
    
    return {"message": "验证码已发送"}


@router.post("/verify-phone")
async def verify_phone(
    request: PhoneVerificationConfirm,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """验证手机号"""
    if not sms_service.verify_code(request.phone, request.code):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="验证码错误或已过期"
        )
    
    # 检查手机号是否已被其他用户使用
    existing_user = db.query(User).filter(
        User.phone == request.phone,
        User.id != current_user.id
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该手机号已被其他用户使用"
        )
    
    # 更新用户手机号和认证等级
    current_user.phone = request.phone
    if current_user.verification_level == VerificationLevel.NONE:
        current_user.verification_level = VerificationLevel.BASIC
        current_user.is_verified = True
    
    db.commit()
    
    return {"message": "手机号验证成功"}


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """登出（客户端删除token即可）"""
    return {"message": "登出成功"}
