#!/usr/bin/env python3
"""
数据种子文件 - 插入测试数据
运行方式: python seed_data.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.user import User, UserType, VerificationLevel
from app.models.task import Task, TaskCategory, TaskStatus
from app.models.order import Order, OrderStatus
from datetime import datetime, timedelta


def create_test_users(db: Session):
    """创建测试用户"""
    users = [
        {
            "openid": "test_merchant_001",
            "nickname": "张老板",
            "avatar_url": "https://example.com/avatar1.jpg",
            "phone": "13800138001",
            "user_type": UserType.MERCHANT,
            "real_name": "张三",
            "id_card": "110101199001011234",
            "verification_level": VerificationLevel.ADVANCED,
            "is_verified": True,
            "credit_score": 95.5,
            "latitude": 39.9042,
            "longitude": 116.4074,
            "address": "北京市朝阳区建国门外大街1号"
        },
        {
            "openid": "test_worker_001",
            "nickname": "李师傅",
            "avatar_url": "https://example.com/avatar2.jpg",
            "phone": "13800138002",
            "user_type": UserType.WORKER,
            "real_name": "李四",
            "id_card": "110101199002021234",
            "verification_level": VerificationLevel.BASIC,
            "is_verified": True,
            "credit_score": 88.0,
            "latitude": 39.9000,
            "longitude": 116.4000,
            "address": "北京市朝阳区三里屯"
        },
        {
            "openid": "test_both_001",
            "nickname": "王经理",
            "avatar_url": "https://example.com/avatar3.jpg",
            "phone": "13800138003",
            "user_type": UserType.BOTH,
            "real_name": "王五",
            "id_card": "110101199003031234",
            "verification_level": VerificationLevel.ADVANCED,
            "is_verified": True,
            "credit_score": 92.0,
            "latitude": 39.9100,
            "longitude": 116.4100,
            "address": "北京市海淀区中关村"
        },
        {
            "openid": "test_worker_002",
            "nickname": "赵师傅",
            "avatar_url": "https://example.com/avatar4.jpg",
            "phone": "13800138004",
            "user_type": UserType.WORKER,
            "real_name": "赵六",
            "id_card": "110101199004041234",
            "verification_level": VerificationLevel.BASIC,
            "is_verified": True,
            "credit_score": 85.5,
            "latitude": 39.8800,
            "longitude": 116.3800,
            "address": "北京市西城区西单"
        }
    ]
    
    created_users = []
    for user_data in users:
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.openid == user_data["openid"]).first()
        if not existing_user:
            user = User(**user_data)
            db.add(user)
            db.flush()  # 获取ID但不提交
            created_users.append(user)
            print(f"创建用户: {user.nickname} (ID: {user.id})")
        else:
            created_users.append(existing_user)
            print(f"用户已存在: {existing_user.nickname} (ID: {existing_user.id})")
    
    return created_users


def create_test_tasks(db: Session, users: list):
    """创建测试任务"""
    # 找到商户用户
    merchants = [u for u in users if u.user_type in [UserType.MERCHANT, UserType.BOTH]]
    
    tasks_data = [
        {
            "publisher_id": merchants[0].id,
            "title": "办公室深度清洁",
            "description": "需要对200平米办公室进行深度清洁，包括地面、桌椅、窗户等。要求有经验的清洁师傅。",
            "category": TaskCategory.CLEANING,
            "reward": 300.0,
            "estimated_hours": 4.0,
            "deadline": datetime.now() + timedelta(days=3),
            "latitude": 39.9042,
            "longitude": 116.4074,
            "address": "北京市朝阳区建国门外大街1号国贸大厦",
            "auto_accept": False,
            "status": TaskStatus.PENDING
        },
        {
            "publisher_id": merchants[0].id,
            "title": "搬运办公用品",
            "description": "需要将一批办公用品从仓库搬运到办公室，约50箱文件和办公用品。",
            "category": TaskCategory.MOVING,
            "reward": 200.0,
            "estimated_hours": 2.0,
            "deadline": datetime.now() + timedelta(days=1),
            "latitude": 39.9000,
            "longitude": 116.4000,
            "address": "北京市朝阳区三里屯SOHO",
            "auto_accept": True,
            "status": TaskStatus.PENDING
        },
        {
            "publisher_id": merchants[1].id if len(merchants) > 1 else merchants[0].id,
            "title": "电脑维修",
            "description": "公司电脑出现故障，需要专业维修师傅上门检修。",
            "category": TaskCategory.REPAIR,
            "reward": 150.0,
            "estimated_hours": 1.5,
            "deadline": datetime.now() + timedelta(hours=12),
            "latitude": 39.9100,
            "longitude": 116.4100,
            "address": "北京市海淀区中关村科技园",
            "auto_accept": False,
            "status": TaskStatus.PENDING
        },
        {
            "publisher_id": merchants[0].id,
            "title": "外卖配送",
            "description": "需要配送20份午餐到附近写字楼，路程较近。",
            "category": TaskCategory.DELIVERY,
            "reward": 80.0,
            "estimated_hours": 1.0,
            "deadline": datetime.now() + timedelta(hours=2),
            "latitude": 39.8900,
            "longitude": 116.3900,
            "address": "北京市西城区金融街",
            "auto_accept": True,
            "status": TaskStatus.PENDING
        },
        {
            "publisher_id": merchants[0].id,
            "title": "家具安装",
            "description": "新购买的办公桌椅需要安装，共6套桌椅。",
            "category": TaskCategory.ASSEMBLY,
            "reward": 250.0,
            "estimated_hours": 3.0,
            "deadline": datetime.now() + timedelta(days=2),
            "latitude": 39.9200,
            "longitude": 116.4200,
            "address": "北京市朝阳区望京SOHO",
            "auto_accept": False,
            "status": TaskStatus.PENDING
        }
    ]
    
    created_tasks = []
    for task_data in tasks_data:
        task = Task(**task_data)
        db.add(task)
        db.flush()
        created_tasks.append(task)
        print(f"创建任务: {task.title} (ID: {task.id})")
    
    return created_tasks


def create_test_orders(db: Session, tasks: list, users: list):
    """创建测试订单"""
    # 找到零工用户
    workers = [u for u in users if u.user_type in [UserType.WORKER, UserType.BOTH]]
    
    if len(tasks) >= 2 and len(workers) >= 1:
        # 创建一个已完成的订单
        completed_order = Order(
            task_id=tasks[0].id,
            worker_id=workers[0].id,
            merchant_id=tasks[0].publisher_id,
            status=OrderStatus.COMPLETED,
            accepted_at=datetime.now() - timedelta(days=2),
            confirmed_at=datetime.now() - timedelta(days=2, hours=1),
            started_at=datetime.now() - timedelta(days=2, hours=1),
            worker_completed_at=datetime.now() - timedelta(days=1, hours=2),
            merchant_completed_at=datetime.now() - timedelta(days=1, hours=1),
            completed_at=datetime.now() - timedelta(days=1, hours=1),
            worker_rating=5,
            merchant_rating=4,
            worker_comment="工作认真负责，质量很好！",
            merchant_comment="按时完成，服务态度好。"
        )
        db.add(completed_order)
        
        # 更新对应任务状态
        tasks[0].status = TaskStatus.COMPLETED
        
        print(f"创建已完成订单: 任务ID {tasks[0].id} - 零工ID {workers[0].id}")
        
        # 创建一个进行中的订单
        if len(workers) >= 2:
            in_progress_order = Order(
                task_id=tasks[1].id,
                worker_id=workers[1].id if len(workers) > 1 else workers[0].id,
                merchant_id=tasks[1].publisher_id,
                status=OrderStatus.IN_PROGRESS,
                accepted_at=datetime.now() - timedelta(hours=2),
                started_at=datetime.now() - timedelta(hours=1)
            )
            db.add(in_progress_order)
            
            # 更新对应任务状态
            tasks[1].status = TaskStatus.IN_PROGRESS
            
            print(f"创建进行中订单: 任务ID {tasks[1].id} - 零工ID {workers[1].id if len(workers) > 1 else workers[0].id}")


def main():
    """主函数"""
    print("开始插入测试数据...")
    
    db = SessionLocal()
    try:
        # 创建测试用户
        print("\n1. 创建测试用户...")
        users = create_test_users(db)
        
        # 创建测试任务
        print("\n2. 创建测试任务...")
        tasks = create_test_tasks(db, users)
        
        # 创建测试订单
        print("\n3. 创建测试订单...")
        create_test_orders(db, tasks, users)
        
        # 提交所有更改
        db.commit()
        print("\n✅ 测试数据插入完成！")
        
        print(f"\n📊 数据统计:")
        print(f"- 用户数量: {db.query(User).count()}")
        print(f"- 任务数量: {db.query(Task).count()}")
        print(f"- 订单数量: {db.query(Order).count()}")
        
    except Exception as e:
        db.rollback()
        print(f"\n❌ 插入数据时出错: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    main()
