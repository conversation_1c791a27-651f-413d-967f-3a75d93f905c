from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, Enum
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class UserType(enum.Enum):
    MERCHANT = "merchant"  # 商户
    WORKER = "worker"      # 零工
    BOTH = "both"          # 双重身份


class VerificationLevel(enum.Enum):
    NONE = "none"          # 未认证
    BASIC = "basic"        # 基础认证（手机号）
    ADVANCED = "advanced"  # 高级认证（身份证）
    PREMIUM = "premium"    # 高级认证（人脸识别）


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    openid = Column(String(100), unique=True, index=True, nullable=False)
    nickname = Column(String(50), nullable=False)
    avatar_url = Column(String(500))
    phone = Column(String(20), unique=True, index=True)
    user_type = Column(Enum(UserType), default=UserType.WORKER)

    # 实名认证
    real_name = Column(String(20))
    id_card = Column(String(18))
    verification_level = Column(Enum(VerificationLevel), default=VerificationLevel.NONE)
    is_verified = Column(Boolean, default=False)  # 保留兼容性

    # 扩展认证字段（预留）
    id_card_front_url = Column(String(500))  # 身份证正面照片
    id_card_back_url = Column(String(500))   # 身份证反面照片
    face_verification_status = Column(Boolean, default=False)  # 人脸验证状态

    # 信用评分
    credit_score = Column(Float, default=100.0)

    # 地理位置
    latitude = Column(Float)
    longitude = Column(Float)
    address = Column(String(200))

    # 状态
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
