import math
from typing import <PERSON><PERSON>


def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    计算两个地理坐标之间的距离（公里）
    使用Haversine公式
    """
    # 将度数转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # 计算差值
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    # Haversine公式
    a = (math.sin(dlat / 2) ** 2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2)
    c = 2 * math.asin(math.sqrt(a))
    
    # 地球半径（公里）
    earth_radius = 6371.0
    
    # 计算距离
    distance = earth_radius * c
    return round(distance, 2)


def get_bounding_box(lat: float, lon: float, distance_km: float) -> <PERSON><PERSON>[float, float, float, float]:
    """
    根据中心点和距离计算边界框
    返回 (min_lat, max_lat, min_lon, max_lon)
    """
    # 地球半径（公里）
    earth_radius = 6371.0
    
    # 纬度变化（度）
    lat_change = distance_km / earth_radius * (180 / math.pi)
    
    # 经度变化（度）- 考虑纬度影响
    lon_change = distance_km / (earth_radius * math.cos(math.radians(lat))) * (180 / math.pi)
    
    min_lat = lat - lat_change
    max_lat = lat + lat_change
    min_lon = lon - lon_change
    max_lon = lon + lon_change
    
    return min_lat, max_lat, min_lon, max_lon


def is_within_distance(
    lat1: float, lon1: float, 
    lat2: float, lon2: float, 
    max_distance_km: float
) -> bool:
    """
    检查两个坐标是否在指定距离内
    """
    distance = calculate_distance(lat1, lon1, lat2, lon2)
    return distance <= max_distance_km
